#include "Int_LEDMatrix.h"
#include <STC89C5xRC.H>
#define LED_MATRIX_EN P35
#define LED_EN P34
#define SER P10
#define SCK P12
#define RCK P11
static u8 s_buffer[8];

// 字符点阵数据 (8x8)
// 字符 'F' 的点阵数据
static u8 code char_F[8] = {
    0xFF,  // 11111111
    0x80,  // 10000000
    0x80,  // 10000000
    0xFE,  // 11111110
    0x80,  // 10000000
    0x80,  // 10000000
    0x80,  // 10000000
    0x80   // 10000000
};

// 字符 'B' 的点阵数据
static u8 code char_B[8] = {
    0xFE,  // 11111110
    0x82,  // 10000010
    0x82,  // 10000010
    0xFE,  // 11111110
    0x82,  // 10000010
    0x82,  // 10000010
    0x82,  // 10000010
    0xFE   // 11111110
};
void Int_LEDMatrix_Init()
{
    LED_MATRIX_EN=0;
    LED_EN=0;
}

void Int_LEDMatrix_SetPic(u8 pic[])
{
    u8 i;
    for ( i = 0; i < 8; i++)
    {
        s_buffer[i]=pic[i];
    }
    
}

void Int_LEDMatrix_Refresh()
{
  u8 i, j;
  for ( i = 0; i < 8; i++)
  {
    // 清空移位寄存器
    for(j = 0; j < 8; j++)
    {
      if(j == i)
      {
        SER = 1;  // 设置当前行为高电平
      }
      else
      {
        SER = 0;  // 其他行为低电平
      }

      // 移位时钟
      SCK = 0;
      SCK = 1;
    }

    // 锁存时钟，将移位寄存器的数据输出到并行口
    RCK = 0;
    RCK = 1;

    // 设置当前行显示的数据（取反是因为共阳极LED）
    P0 = ~s_buffer[i];

    // 使能LED矩阵显示
    LED_MATRIX_EN = 1;
    LED_EN = 1;

    // 延时一段时间让当前行显示
    Com_Util_Delay1ms(2);

    // 关闭显示，准备下一行
    LED_MATRIX_EN = 0;
    LED_EN = 0;
  }
}

// 显示单个字符
void Int_LEDMatrix_ShowChar(u8 ch)
{
    u8 i;
    u8* char_data;

    // 根据字符选择对应的点阵数据
    switch(ch)
    {
        case 'F':
        case 'f':
            char_data = (u8*)char_F;
            break;
        case 'B':
        case 'b':
            char_data = (u8*)char_B;
            break;
        default:
            // 如果是未定义字符，显示空白
            for(i = 0; i < 8; i++)
            {
                s_buffer[i] = 0x00;
            }
            return;
    }

    // 将字符数据复制到显示缓冲区
    for(i = 0; i < 8; i++)
    {
        s_buffer[i] = char_data[i];
    }
}

// 显示字符串（逐个字符显示）
void Int_LEDMatrix_ShowString(u8* str)
{
    u8 i = 0;
    while(str[i] != '\0')
    {
        Int_LEDMatrix_ShowChar(str[i]);

        // 显示当前字符一段时间
        u16 display_time = 0;
        while(display_time < 500)  // 显示约1秒
        {
            Int_LEDMatrix_Refresh();
            display_time++;
        }
        i++;
    }
}

// 循环显示字符串
void Int_LEDMatrix_CyclicDisplay(u8* str, u16 delay_ms)
{
    u8 i = 0;
    u8 str_len = 0;

    // 计算字符串长度
    while(str[str_len] != '\0')
    {
        str_len++;
    }

    while(1)  // 无限循环
    {
        // 显示当前字符
        Int_LEDMatrix_ShowChar(str[i]);

        // 显示指定时间
        u16 display_time = 0;
        while(display_time < delay_ms)
        {
            Int_LEDMatrix_Refresh();
            display_time++;
        }

        // 移动到下一个字符
        i++;
        if(i >= str_len)
        {
            i = 0;  // 回到字符串开头，实现循环
        }
    }
}
