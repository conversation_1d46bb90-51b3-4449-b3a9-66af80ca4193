#include "Int_LEDMatrix.h"
#include <STC89C5xRC.H>
#include "Com_Util.h"
#define LED_MATRIX_EN P35
#define LED_EN P34
#define SER P10
#define SCK P12
#define RCK P11
static u8 s_buffer[8];
void Int_LEDMatrix_Init()
{
    LED_MATRIX_EN=0;
    LED_EN=0;
}

void Int_LEDMatrix_SetPic(u8 pic[])
{
    u8 i;
    for ( i = 0; i < 8; i++)
    {
        s_buffer[i]=pic[i];
    }
    
}

void Int_LEDMatrix_Refresh()
{
  u8 i, j;
  for ( i = 0; i < 8; i++)
  {
    // 清空移位寄存器
    for(j = 0; j < 8; j++)
    {
      if(j == i)
      {
        SER = 1;  // 设置当前行为高电平
      }
      else
      {
        SER = 0;  // 其他行为低电平
      }

      // 移位时钟
      SCK = 0;
      SCK = 1;
    }

    // 锁存时钟，将移位寄存器的数据输出到并行口
    RCK = 0;
    RCK = 1;

    // 设置当前行显示的数据（取反是因为共阳极LED）
    P0 = ~s_buffer[i];

    // 使能LED矩阵显示
    LED_MATRIX_EN = 1;
    LED_EN = 1;

    // 延时一段时间让当前行显示
    Com_Util_Delay1ms(2);

    // 关闭显示，准备下一行
    LED_MATRIX_EN = 0;
    LED_EN = 0;
  }
}
