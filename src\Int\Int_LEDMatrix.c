#include "Int_LEDMatrix.h"
#include <STC89C5xRC.H>
#include <INTRINS.H>
#define LED_MATRIX_EN P35
#define LED_EN P34
#define SER P10
#define SCK P12
#define RCK P11
static u8 s_buffer[8];

// 字符点阵数据 (8x8) - 为滚动显示优化
// 字符 'F' 的点阵数据 (6位宽度，便于滚动)
static u8 code char_F[8] = {
    0xFC,  // 11111100 - 顶部横线
    0x80,  // 10000000 - 左边竖线
    0x80,  // 10000000 - 左边竖线
    0xF8,  // 11111000 - 中间横线
    0x80,  // 10000000 - 左边竖线
    0x80,  // 10000000 - 左边竖线
    0x80,  // 10000000 - 左边竖线
    0x80   // 10000000 - 左边竖线
};

// 字符 'B' 的点阵数据 (6位宽度，便于滚动)
static u8 code char_B[8] = {
    0xF8,  // 11111000 - 顶部横线
    0x88,  // 10001000 - 左右竖线
    0x88,  // 10001000 - 左右竖线
    0xF8,  // 11111000 - 中间横线
    0x88,  // 10001000 - 左右竖线
    0x88,  // 10001000 - 左右竖线
    0x88,  // 10001000 - 左右竖线
    0xF8   // 11111000 - 底部横线
};

// 空白字符，用于字符间隔
static u8 code char_SPACE[8] = {
    0x00,  // 00000000
    0x00,  // 00000000
    0x00,  // 00000000
    0x00,  // 00000000
    0x00,  // 00000000
    0x00,  // 00000000
    0x00,  // 00000000
    0x00   // 00000000
};

// 微秒级延时函数，用于精确控制显示时序
void delay_us(u16 us)
{
    while(us--)
    {
        // 空操作，约1微秒延时（根据晶振频率调整）
        _nop_();
        _nop_();
    }
}
void Int_LEDMatrix_Init()
{
    u8 i;

    // 关闭所有LED显示
    LED_MATRIX_EN = 0;
    LED_EN = 0;

    // 清空显示缓冲区，确保没有流水灯效果
    for(i = 0; i < 8; i++)
    {
        s_buffer[i] = 0x00;  // 全部清零
    }

    // 清空数据端口
    P0 = 0xFF;  // 对于共阳极，全1表示全灭

    // 初始化移位寄存器控制信号
    SER = 0;
    SCK = 0;
    RCK = 0;

    // 清空移位寄存器
    for(i = 0; i < 8; i++)
    {
        SER = 0;
        SCK = 0;
        SCK = 1;
    }

    // 锁存清空的数据
    RCK = 0;
    RCK = 1;
}

void Int_LEDMatrix_SetPic(u8 pic[])
{
    u8 i;
    for ( i = 0; i < 8; i++)
    {
        s_buffer[i]=pic[i];
    }
    
}

void Int_LEDMatrix_Refresh()
{
  u8 i, j;

  for ( i = 0; i < 8; i++)
  {
    // 先关闭所有显示，确保没有流水灯效果
    LED_MATRIX_EN = 0;
    LED_EN = 0;
    P0 = 0xFF;  // 共阳极全灭

    // 清空移位寄存器，确保只有当前行被选中
    for(j = 0; j < 8; j++)
    {
      if(j == i)
      {
        SER = 1;  // 设置当前行为高电平
      }
      else
      {
        SER = 0;  // 其他行为低电平，防止流水灯
      }

      // 移位时钟
      SCK = 0;
      delay_us(1);
      SCK = 1;
      delay_us(1);
    }

    // 锁存行选择数据
    RCK = 0;
    delay_us(1);
    RCK = 1;
    delay_us(1);

    // 设置当前行的列数据（取反是因为共阳极LED）
    P0 = ~s_buffer[i];

    // 使能LED矩阵显示当前行
    LED_MATRIX_EN = 1;
    LED_EN = 1;

    // 每行显示时间
    delay_us(500);

    // 关闭当前行显示，准备下一行
    LED_MATRIX_EN = 0;
    LED_EN = 0;
    P0 = 0xFF;  // 确保关闭
  }
}

// 显示单个字符
void Int_LEDMatrix_ShowChar(u8 ch)
{
    u8 i;
    u8* char_data;

    // 根据字符选择对应的点阵数据
    switch(ch)
    {
        case 'F':
        case 'f':
            char_data = (u8*)char_F;
            break;
        case 'B':
        case 'b':
            char_data = (u8*)char_B;
            break;
        default:
            // 如果是未定义字符，显示空白
            for(i = 0; i < 8; i++)
            {
                s_buffer[i] = 0x00;
            }
            return;
    }

    // 将字符数据复制到显示缓冲区
    for(i = 0; i < 8; i++)
    {
        s_buffer[i] = char_data[i];
    }
}

// 显示字符串（逐个字符显示）
void Int_LEDMatrix_ShowString(u8* str)
{
    u8 i = 0;
    u16 display_time;

    while(str[i] != '\0')
    {
        Int_LEDMatrix_ShowChar(str[i]);

        // 显示当前字符一段时间
        display_time = 0;
        while(display_time < 500)  // 显示约1秒
        {
            Int_LEDMatrix_Refresh();
            display_time++;
        }
        i++;
    }
}

// 循环显示字符串 - 改进版本，实现真正的动态显示
void Int_LEDMatrix_CyclicDisplay(u8* str, u16 delay_ms)
{
    u8 i = 0;
    u8 str_len = 0;
    u16 refresh_count = 0;
    u16 char_display_cycles;

    // 计算字符串长度
    while(str[str_len] != '\0')
    {
        str_len++;
    }

    // 计算每个字符需要显示多少个刷新周期
    // 每个刷新周期约8ms（8行 * 1ms），所以delay_ms/8 = 刷新周期数
    char_display_cycles = delay_ms / 8;
    if(char_display_cycles < 10) char_display_cycles = 10; // 最少10个周期

    while(1)  // 无限循环
    {
        // 显示当前字符
        Int_LEDMatrix_ShowChar(str[i]);

        // 连续刷新显示指定次数
        refresh_count = 0;
        while(refresh_count < char_display_cycles)
        {
            Int_LEDMatrix_Refresh();
            refresh_count++;
        }

        // 移动到下一个字符
        i++;
        if(i >= str_len)
        {
            i = 0;  // 回到字符串开头，实现循环
        }
    }
}

// 获取字符点阵数据的函数
u8* GetCharData(u8 ch)
{
    switch(ch)
    {
        case 'F':
        case 'f':
            return (u8*)char_F;
        case 'B':
        case 'b':
            return (u8*)char_B;
        case ' ':
            return (u8*)char_SPACE;
        default:
            return (u8*)char_SPACE;
    }
}

// 滚动显示字符串 - 简化版本
void Int_LEDMatrix_ScrollDisplay(u8* str, u16 scroll_speed)
{
    u8 str_len = 0;
    u8 scroll_pos = 0;
    u8 i, j;
    u8 refresh_count;
    u8* char_data;
    u8 current_char_index;
    u8 next_char_index;
    u8* current_char_data;
    u8* next_char_data;

    // 计算字符串长度
    while(str[str_len] != '\0')
    {
        str_len++;
    }

    while(1)  // 无限滚动
    {
        // 计算当前显示的字符索引
        current_char_index = (scroll_pos / 8) % str_len;
        next_char_index = ((scroll_pos / 8) + 1) % str_len;

        // 获取当前字符和下一个字符的点阵数据
        current_char_data = GetCharData(str[current_char_index]);
        next_char_data = GetCharData(str[next_char_index]);

        // 计算在字符内的滚动位置
        u8 char_scroll_pos = scroll_pos % 8;

        // 构建滚动显示缓冲区
        for(i = 0; i < 8; i++)  // 8行
        {
            if(char_scroll_pos == 0)
            {
                // 完整显示当前字符
                s_buffer[i] = current_char_data[i];
            }
            else
            {
                // 滚动显示：当前字符左移 + 下一个字符右移
                s_buffer[i] = (current_char_data[i] << char_scroll_pos) |
                             (next_char_data[i] >> (8 - char_scroll_pos));
            }
        }

        // 刷新显示
        refresh_count = 0;
        while(refresh_count < scroll_speed)
        {
            Int_LEDMatrix_Refresh();
            refresh_count++;
        }

        // 移动滚动位置
        scroll_pos++;

        // 当滚动完所有字符后重新开始
        if(scroll_pos >= str_len * 8)
        {
            scroll_pos = 0;
        }
    }
}
