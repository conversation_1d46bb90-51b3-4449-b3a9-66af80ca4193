#include "Int_LEDMatrix.h"
#include <STC89C5xRC.H>
#include <INTRINS.H>
#define LED_MATRIX_EN P35
#define LED_EN P34
#define SER P10
#define SCK P12
#define RCK P11
static u8 s_buffer[8];

// 字符点阵数据 (8x8) - 优化版本，减少鬼影
// 字符 'F' 的点阵数据
static u8 code char_F[8] = {
    0xFF,  // 11111111 - 顶部横线
    0x80,  // 10000000 - 左边竖线
    0x80,  // 10000000 - 左边竖线
    0xFC,  // 11111100 - 中间横线（稍短避免鬼影）
    0x80,  // 10000000 - 左边竖线
    0x80,  // 10000000 - 左边竖线
    0x80,  // 10000000 - 左边竖线
    0x80   // 10000000 - 左边竖线
};

// 字符 'B' 的点阵数据
static u8 code char_B[8] = {
    0xFC,  // 11111100 - 顶部横线
    0x84,  // 10000100 - 左右竖线
    0x84,  // 10000100 - 左右竖线
    0xFC,  // 11111100 - 中间横线
    0x84,  // 10000100 - 左右竖线
    0x84,  // 10000100 - 左右竖线
    0x84,  // 10000100 - 左右竖线
    0xFC   // 11111100 - 底部横线
};

// 微秒级延时函数，用于精确控制显示时序
void delay_us(u16 us)
{
    while(us--)
    {
        // 空操作，约1微秒延时（根据晶振频率调整）
        _nop_();
        _nop_();
    }
}
void Int_LEDMatrix_Init()
{
    LED_MATRIX_EN=0;
    LED_EN=0;
}

void Int_LEDMatrix_SetPic(u8 pic[])
{
    u8 i;
    for ( i = 0; i < 8; i++)
    {
        s_buffer[i]=pic[i];
    }
    
}

void Int_LEDMatrix_Refresh()
{
  u8 i, j;
  for ( i = 0; i < 8; i++)
  {
    // 先关闭显示，避免鬼影
    LED_MATRIX_EN = 0;
    LED_EN = 0;

    // 设置当前行显示的数据（取反是因为共阳极LED）
    P0 = ~s_buffer[i];

    // 清空移位寄存器
    for(j = 0; j < 8; j++)
    {
      if(j == i)
      {
        SER = 1;  // 设置当前行为高电平
      }
      else
      {
        SER = 0;  // 其他行为低电平
      }

      // 移位时钟，添加小延时确保稳定
      SCK = 0;
      delay_us(1);
      SCK = 1;
      delay_us(1);
    }

    // 锁存时钟，将移位寄存器的数据输出到并行口
    RCK = 0;
    delay_us(1);
    RCK = 1;
    delay_us(1);

    // 使能LED矩阵显示
    LED_MATRIX_EN = 1;
    LED_EN = 1;

    // 每行显示时间，平衡亮度和刷新率
    delay_us(500);  // 500微秒，比1ms更精确
  }

  // 完成一帧显示后关闭
  LED_MATRIX_EN = 0;
  LED_EN = 0;
}

// 显示单个字符
void Int_LEDMatrix_ShowChar(u8 ch)
{
    u8 i;
    u8* char_data;

    // 根据字符选择对应的点阵数据
    switch(ch)
    {
        case 'F':
        case 'f':
            char_data = (u8*)char_F;
            break;
        case 'B':
        case 'b':
            char_data = (u8*)char_B;
            break;
        default:
            // 如果是未定义字符，显示空白
            for(i = 0; i < 8; i++)
            {
                s_buffer[i] = 0x00;
            }
            return;
    }

    // 将字符数据复制到显示缓冲区
    for(i = 0; i < 8; i++)
    {
        s_buffer[i] = char_data[i];
    }
}

// 显示字符串（逐个字符显示）
void Int_LEDMatrix_ShowString(u8* str)
{
    u8 i = 0;
    u16 display_time;

    while(str[i] != '\0')
    {
        Int_LEDMatrix_ShowChar(str[i]);

        // 显示当前字符一段时间
        display_time = 0;
        while(display_time < 500)  // 显示约1秒
        {
            Int_LEDMatrix_Refresh();
            display_time++;
        }
        i++;
    }
}

// 循环显示字符串 - 改进版本，实现真正的动态显示
void Int_LEDMatrix_CyclicDisplay(u8* str, u16 delay_ms)
{
    u8 i = 0;
    u8 str_len = 0;
    u16 refresh_count = 0;
    u16 char_display_cycles;

    // 计算字符串长度
    while(str[str_len] != '\0')
    {
        str_len++;
    }

    // 计算每个字符需要显示多少个刷新周期
    // 每个刷新周期约8ms（8行 * 1ms），所以delay_ms/8 = 刷新周期数
    char_display_cycles = delay_ms / 8;
    if(char_display_cycles < 10) char_display_cycles = 10; // 最少10个周期

    while(1)  // 无限循环
    {
        // 显示当前字符
        Int_LEDMatrix_ShowChar(str[i]);

        // 连续刷新显示指定次数
        refresh_count = 0;
        while(refresh_count < char_display_cycles)
        {
            Int_LEDMatrix_Refresh();
            refresh_count++;
        }

        // 移动到下一个字符
        i++;
        if(i >= str_len)
        {
            i = 0;  // 回到字符串开头，实现循环
        }
    }
}
