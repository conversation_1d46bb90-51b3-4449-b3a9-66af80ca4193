{"name": "LED静态显示", "type": "C51", "dependenceList": [], "srcDirs": ["src"], "outDir": "build", "virtualFolder": {"name": "<virtual_root>", "files": [], "folders": []}, "miscInfo": {"uid": "e800582245ae6736bd0111405a633bd2"}, "targets": {"Release": {"excludeList": [], "toolchain": "Keil_C51", "compileConfig": {"options": ""}, "uploader": "Custom", "uploadConfig": {"bin": "", "commandLine": "python ./tools/stcflash.py -p ${port} \"${hexFile}\"", "eraseChipCommand": ""}, "uploadConfigMap": {}, "custom_dep": {"name": "default", "incList": ["c:/Program Files/Keil_v5/C51/INC/STC", "../../../KEIL5/C51/INC/STC"], "libList": [], "defineList": []}, "builderOptions": {"Keil_C51": {"version": 2, "beforeBuildTasks": [], "afterBuildTasks": [], "global": {"ram-mode": "SMALL", "rom-mode": "LARGE"}, "c/cpp-compiler": {"optimization-type": "SPEED", "optimization-level": "level-8"}, "asm-compiler": {}, "linker": {"remove-unused": true, "output-format": "elf"}}}}}, "version": "3.6", "deviceName": null, "packDir": null}