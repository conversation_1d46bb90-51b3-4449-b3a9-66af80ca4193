#ifndef __INT_LEDMATRIX_H__
#define __INT_LEDMATRIX_H__

#include "../Com/Com_Util.h"

void Int_LEDMatrix_Init();
void Int_LEDMatrix_SetPic(u8 pic[]);
void Int_LEDMatrix_Refresh();
void Int_LEDMatrix_ShowChar(u8 ch);
void Int_LEDMatrix_ShowString(u8* str);
void Int_LEDMatrix_CyclicDisplay(u8* str, u16 delay_ms);
void Int_LEDMatrix_ScrollDisplay(u8* str, u16 scroll_speed);

#endif /* __INT_LEDMATRIX_H__ */